import React, { useEffect, useState, useRef } from "react";
import api from "../axios/axiosInstance";
import {
  <PERSON>aEye,
  FaEdit,
  FaTrash,
  FaSyncAlt,
  FaSearch,
  FaTimes,
} from "react-icons/fa";
import URL from "../axios/URl";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { Link } from "react-router";

const theme = createTheme({
  zIndex: {
    modal: 1500, // Increased from default 1300
  },
});

function formatDate(dateStr) {
  if (!dateStr) return "-";
  const d = new Date(dateStr);
  return d.toLocaleDateString(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

function slugify(text) {
  return text
    .toString()
    .normalize("NFD")
    .replace(/\p{Diacritic}/gu, "")
    .replace(/[^\w\s-]/g, "")
    .trim()
    .replace(/\s+/g, "-")
    .replace(/-+/g, "-")
    .toLowerCase();
}

function BlogManagement() {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedBlog, setSelectedBlog] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState("view");
  const [blogDetails, setBlogDetails] = useState(null);
  const [editForm, setEditForm] = useState({
    title: "",
    keywords: "",
    category: "",
    read_time: "",
    content: "",
  });
  const [editLoading, setEditLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredBlogs, setFilteredBlogs] = useState([]);
  const [sortConfig, setSortConfig] = useState({
    key: "created_at",
    direction: "descending",
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [blogToDelete, setBlogToDelete] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [viewDialogContent, setViewDialogContent] = useState("");
  const [editHtmlContent, setEditHtmlContent] = useState("");
  const editorRef = useRef(null);
  const [isUpdatingTitle, setIsUpdatingTitle] = useState(false);
  const [viewLoading, setViewLoading] = useState(false);
  const [slug, setSlug] = useState("");
  const [slugAvailable, setSlugAvailable] = useState(null); // null = not checked, true = available, false = taken
  const slugCheckTimeout = useRef(null);

  // Prevent background scroll when popup is open
  useEffect(() => {
    if (showModal || viewDialogOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [showModal, viewDialogOpen]);

  const fetchBlogs = () => {
    setLoading(true);
    api
      .get("get-all-blogs/")
      .then((res) => {
        if (Array.isArray(res.data.data)) {
          setBlogs(res.data.data);
          setFilteredBlogs(res.data.data);
        } else {
          setBlogs([]);
          setFilteredBlogs([]);
        }
      })
      .catch(() => {
        setBlogs([]);
        setFilteredBlogs([]);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchBlogs();
  }, []);

  useEffect(() => {
    const filtered = blogs.filter(
      (blog) =>
        blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (blog.category &&
          blog.category.toLowerCase().includes(searchQuery.toLowerCase()))
    );
    setFilteredBlogs(filtered);
  }, [searchQuery, blogs]);

  // Update HTML content when title changes
  useEffect(() => {
    if (
      editForm.title &&
      editorRef.current &&
      !isUpdatingTitle &&
      modalMode === "edit"
    ) {
      // Only update if the editor already has content (not during initial load)
      if (editorRef.current.innerHTML.trim() !== "") {
        setIsUpdatingTitle(true);

        // Find the first h1 tag in the editor and update its content
        const h1Element = editorRef.current.querySelector("h1");
        if (h1Element) {
          // Only update the text content of the h1, don't replace the entire HTML
          h1Element.textContent = editForm.title;
        } else {
          // If no h1 exists, create one at the beginning without replacing existing content
          const currentContent = editorRef.current.innerHTML;
          const newH1 = `<h1>${editForm.title}</h1>`;
          editorRef.current.innerHTML = newH1 + currentContent;
        }

        // Update the content state without triggering another update
        const newContent = editorRef.current.innerHTML;
        setEditHtmlContent(newContent);
        setEditForm((prev) => ({ ...prev, content: newContent }));

        // Reset the flag after a short delay
        setTimeout(() => {
          setIsUpdatingTitle(false);
        }, 100);
      }
    }
  }, [editForm.title, modalMode]);

  useEffect(() => {
    if (modalMode !== "edit" || !editForm.title) {
      setSlug("");
      setSlugAvailable(null);
      return;
    }
    const newSlug = slugify(editForm.title);
    setSlug(newSlug);
    // Debounce API call
    if (slugCheckTimeout.current) clearTimeout(slugCheckTimeout.current);
    slugCheckTimeout.current = setTimeout(() => {
      if (!newSlug) return;
      api
        .get(`${URL.CHECK_SLUG}?slug=${encodeURIComponent(newSlug)}`)
        .then((res) => {
          setSlugAvailable(res.data?.status ?? false);
        })
        .catch(() => setSlugAvailable(false));
    }, 400);
    return () => {
      if (slugCheckTimeout.current) clearTimeout(slugCheckTimeout.current);
    };
  }, [editForm.title, modalMode]);

  const handleSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });

    const sorted = [...filteredBlogs].sort((a, b) => {
      if (a[key] < b[key]) {
        return direction === "ascending" ? -1 : 1;
      }
      if (a[key] > b[key]) {
        return direction === "ascending" ? 1 : -1;
      }
      return 0;
    });

    setFilteredBlogs(sorted);
  };

  const handleView = (blog) => {
    setSelectedBlog(blog);
    setModalMode("view");
    setShowModal(false);
    setBlogDetails(null);
    setViewDialogContent("");
    setViewDialogOpen(true);
    setViewLoading(true);

    api
      .get(`get-blog-by-id/?id=${blog.id}`)
      .then((res) => {
        if (res.data && res.data.data && res.data.data.content) {
          setViewDialogContent(res.data.data.content);
        } else {
          setViewDialogContent(
            "<div style='padding:2rem;color:#667085'>No content found.</div>"
          );
        }
      })
      .catch(() => {
        setViewDialogContent(
          "<div style='padding:2rem;color:#667085'>Failed to load blog content.</div>"
        );
      })
      .finally(() => {
        setViewLoading(false);
      });
  };

  const setEditorContent = (content) => {
    if (editorRef.current) {
      // Set the content first
      editorRef.current.innerHTML = content;

      // Try to preserve the cursor position if there's an active selection
      try {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const startOffset = range.startOffset;
          const startContainer = range.startContainer;

          // Try to restore cursor position
          if (startContainer && startContainer.parentNode) {
            const newRange = document.createRange();
            newRange.setStart(startContainer, startOffset);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);
          }
        } else {
          // If no selection, just focus the editor
          editorRef.current.focus();
        }
      } catch (e) {
        // If cursor restoration fails, just focus the editor
        editorRef.current.focus();
      }
    }
  };

  const handleEdit = (blog) => {
    setSelectedBlog(blog);
    setModalMode("edit");
    setShowModal(true);
    setBlogDetails(null);
    setEditHtmlContent("");

    // Get the blog details
    api
      .get(`get-blog-by-id/?id=${blog.id}`)
      .then((res) => {
        console.log("API Response:", res.data); // Debug log
        const blogData = res.data.data || res.data; // Handle both response structures
        console.log("Blog Data:", blogData); // Debug log
        setBlogDetails(blogData);

        // Populate the edit form with current blog data
        setEditForm({
          title: blogData.title || blog.title || "",
          keywords: blogData.keywords || blog.keywords || "",
          category: blogData.category || blog.category || "",
          read_time: blogData.read_time || blog.read_time || "",
          content: blogData.content || blog.content || "",
        });

        // Set the HTML content for the editor - use the content from API response
        const htmlContent = blogData.content || "";
        console.log("HTML Content to set:", htmlContent); // Debug log
        setEditHtmlContent(htmlContent);

        // Set the content directly to the editor after a short delay
        setTimeout(() => {
          setEditorContent(htmlContent);
        }, 100);
      })
      .catch((error) => {
        console.error("API Error:", error); // Debug log
        // Fallback to the blog data from the table if API fails
        setBlogDetails(blog);
        setEditForm({
          title: blog.title || "",
          keywords: blog.keywords || "",
          category: blog.category || "",
          read_time: blog.read_time || "",
          content: blog.content || "",
        });
        setEditHtmlContent(blog.content || "");
        setTimeout(() => {
          setEditorContent(blog.content || "");
        }, 100);
      });
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditForm((prev) => ({ ...prev, [name]: value }));
  };

  const updateEditorContent = () => {
    if (editorRef.current && !isUpdatingTitle) {
      const newContent = editorRef.current.innerHTML;
      setEditHtmlContent(newContent);
      setEditForm((prev) => ({ ...prev, content: newContent }));
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const html = e.clipboardData.getData("text/html");
    const text = e.clipboardData.getData("text/plain");
    let content = html || text;

    if (html) {
      // Create a temporary element to parse HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = html;

      // Clean up the HTML but preserve formatting
      const cleanHtml = (node) => {
        if (node.nodeType === 1) {
          // Element node
          // Preserve important formatting tags
          const preserveTags = [
            "b",
            "strong",
            "i",
            "em",
            "u",
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "p",
            "br",
            "ul",
            "ol",
            "li",
            "blockquote",
            "pre",
            "code",
            "a",
            "img",
          ];

          if (preserveTags.includes(node.tagName.toLowerCase())) {
            // Keep the element but clean its attributes
            const cleanNode = document.createElement(node.tagName);

            // Copy important attributes
            if (node.tagName.toLowerCase() === "a" && node.href) {
              cleanNode.href = node.href;
              cleanNode.target = "_blank";
            }
            if (node.tagName.toLowerCase() === "img" && node.src) {
              cleanNode.src = node.src;
              cleanNode.alt = node.alt || "";
            }

            // Copy text content
            cleanNode.textContent = node.textContent;

            // Recursively process child nodes
            Array.from(node.childNodes).forEach((child) => {
              if (child.nodeType === 1) {
                cleanNode.appendChild(cleanHtml(child));
              } else if (child.nodeType === 3) {
                // Text node
                cleanNode.appendChild(child.cloneNode());
              }
            });

            return cleanNode;
          } else {
            // For non-preserved tags, just return the text content
            return document.createTextNode(node.textContent);
          }
        }
        return node.cloneNode();
      };

      const cleanedContent = cleanHtml(tempDiv);
      content = cleanedContent.innerHTML;
    }

    document.execCommand("insertHTML", false, content);
    updateEditorContent();
  };

  const handleModalClose = () => {
    setShowModal(false);
    setSelectedBlog(null);
    setBlogDetails(null);
    setEditHtmlContent("");
    setEditForm({
      title: "",
      keywords: "",
      category: "",
      read_time: "",
      content: "",
    });
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    setEditLoading(true);
    try {
      const formData = new FormData();
      formData.append("id", selectedBlog.id);
      formData.append("title", editForm.title);
      formData.append("slug", slug);
      formData.append("keywords", editForm.keywords);
      formData.append("category", editForm.category);
      formData.append("read_time", editForm.read_time);
      formData.append("content", editHtmlContent);
      // Note: banner is not included in the edit form, so it will keep the existing value

      await api.put("update-blog/", formData);

      // Update the blogs state with all the new values
      setBlogs((prev) =>
        prev.map((b) =>
          b.id === selectedBlog.id
            ? {
                ...b,
                title: editForm.title,
                keywords: editForm.keywords,
                category: editForm.category,
                read_time: editForm.read_time,
                content: editHtmlContent,
              }
            : b
        )
      );

      handleModalClose();
    } catch (err) {
      alert("Failed to update blog.");
    } finally {
      setEditLoading(false);
    }
  };

  const handleDeleteClick = (blogId) => {
    setBlogToDelete(blogId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!blogToDelete) return;
    try {
      const formData = new FormData();
      formData.append("id", blogToDelete);
      await api.post(URL.DELETE_BLOG, formData);
      setBlogs((prev) => prev.filter((b) => b.id !== blogToDelete));
      setFilteredBlogs((prev) => prev.filter((b) => b.id !== blogToDelete));
    } catch (err) {
      // Optionally show an error dialog or toast
    } finally {
      setDeleteDialogOpen(false);
      setBlogToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setBlogToDelete(null);
  };

  const SortIndicator = ({ columnKey }) => {
    if (sortConfig.key !== columnKey) return null;
    return sortConfig.direction === "ascending" ? "↑" : "↓";
  };

  return (
    <ThemeProvider theme={theme}>
      <div className="min-h-screen bg-white p-4 md:p-8">
        {/* Sticky header */}
        <div className="sticky top-0 z-20 bg-white flex flex-col sm:flex-row items-center justify-between gap-4 px-6 py-4 rounded-xl shadow-md mb-6 border border-brand-200">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-800">
              Blog Management
            </h1>
            <p className="text-gray-600 text-sm mt-1">
              Manage your blog content in one place
            </p>
          </div>

          <div className="flex items-center gap-3 w-full sm:w-auto">
            <div className="relative flex-1 sm:flex-none">
              <input
                type="text"
                placeholder="Search blogs..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 text-gray-700"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  <FaTimes />
                </button>
              )}
            </div>

            <button
              onClick={fetchBlogs}
              className="flex items-center gap-2 px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-all shadow-lg"
              title="Refresh"
            >
              <FaSyncAlt className={loading ? "animate-spin" : ""} />
              <span className="hidden sm:inline">Refresh</span>
            </button>
          </div>
        </div>

        {/* Stats cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-xl shadow p-4 border-l-4 border-brand-500">
            <h3 className="text-gray-600 text-sm font-medium">Total Blogs</h3>
            <p className="text-2xl font-bold text-gray-800 mt-1">
              {blogs.length}
            </p>
          </div>
          <div className="bg-white rounded-xl shadow p-4 border-l-4 border-brand-500">
            <h3 className="text-gray-600 text-sm font-medium">Categories</h3>
            <p className="text-2xl font-bold text-gray-800 mt-1">
              {[...new Set(blogs.map((b) => b.category))].length}
            </p>
          </div>
          <div className="bg-white rounded-xl shadow p-4 border-l-4 border-brand-500">
            <h3 className="text-gray-600 text-sm font-medium">This Month</h3>
            <p className="text-2xl font-bold text-gray-800 mt-1">
              {
                blogs.filter((b) => {
                  const date = new Date(b.created_at);
                  const now = new Date();
                  return (
                    date.getMonth() === now.getMonth() &&
                    date.getFullYear() === now.getFullYear()
                  );
                }).length
              }
            </p>
          </div>
          <div className="bg-white rounded-xl shadow p-4 border-l-4 border-brand-500">
            <h3 className="text-gray-600 text-sm font-medium">
              Avg. Read Time
            </h3>
            <p className="text-2xl font-bold text-gray-800 mt-1">
              {blogs.length
                ? Math.round(
                    blogs.reduce(
                      (sum, b) => sum + parseInt(b.read_time || 0),
                      0
                    ) / blogs.length
                  )
                : 0}
            </p>
          </div>
        </div>

        {/* Table or loading/empty state */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-brand-500 mb-4"></div>
              <span className="text-gray-600">Loading blogs...</span>
            </div>
          ) : filteredBlogs.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="bg-gray-100 border-2 border-dashed rounded-xl w-16 h-16 flex items-center justify-center text-gray-500 mb-4">
                <FaTimes className="text-2xl" />
              </div>
              <span className="text-gray-700 text-lg font-medium">
                No blogs found
              </span>
              <p className="text-gray-500 mt-2">
                {searchQuery
                  ? "Try a different search term"
                  : "Create a new blog to get started"}
              </p>
              {!searchQuery && (
                <Link
                  className="mt-4 px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
                  to={"/admin/blog"}
                >
                  Create Blog
                </Link>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr className="text-gray-700">
                    <th className="px-6 py-3 text-left text-sm font-medium uppercase tracking-wider">
                      Banner
                    </th>
                    <th
                      className="px-6 py-3 text-left text-sm font-medium uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort("title")}
                    >
                      <div className="flex items-center">
                        Title
                        <span className="ml-1">
                          <SortIndicator columnKey="title" />
                        </span>
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-sm font-medium uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort("category")}
                    >
                      <div className="flex items-center">
                        Category
                        <span className="ml-1">
                          <SortIndicator columnKey="category" />
                        </span>
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-sm font-medium uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort("created_at")}
                    >
                      <div className="flex items-center">
                        Date
                        <span className="ml-1">
                          <SortIndicator columnKey="created_at" />
                        </span>
                      </div>
                    </th>
                    <th className="px-6 py-3 text-right text-sm font-medium uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredBlogs.map((blog) => (
                    <tr key={blog.id} className="transition hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        {blog.video_thumbnail ? (
                          <div className="w-16 h-16 flex items-center justify-center overflow-hidden">
                            <img
                              src={
                                blog.video_thumbnail.startsWith("http")
                                  ? blog.video_thumbnail
                                  : `https://flexioninfotech.com${blog.video_thumbnail}`
                              }
                              alt="video thumbnail"
                              className="border-2 border-gray-300 p-[5px] rounded-[20px] lg:rounded-[25px] object-cover h-14 w-14 sm:h-14 sm:w-14 lg:h-16 lg:w-16"
                            />
                          </div>
                        ) : blog.banner ? (
                          <div className="bg-gray-100 border-2 border-dashed rounded-xl w-16 h-16 flex items-center justify-center overflow-hidden">
                            <img
                              src={
                                blog.banner.startsWith("http")
                                  ? blog.banner
                                  : `https://flexioninfotech.com${blog.banner}`
                              }
                              alt="banner"
                              className="border-2 border-[#563d39] p-[5px] rounded-[20px] lg:rounded-[25px] object-cover h-14 w-14 sm:h-14 sm:w-14 lg:h-16 lg:w-16"
                            />
                          </div>
                        ) : (
                          <div className="bg-gray-100 border-2 border-dashed rounded-xl w-16 h-16 flex items-center justify-center text-gray-400">
                            <FaTimes />
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 max-w-xs">
                        <div
                          className="text-sm font-semibold text-gray-900 truncate"
                          title={blog.title}
                        >
                          {blog.title}
                        </div>
                        <div className="text-sm text-gray-500">
                          {blog.keywords}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className="px-2.5 py-0.5 rounded-full text-sm font-medium bg-brand-100 text-brand-700">
                          {blog.category || "Uncategorized"}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-600">
                        <div>{formatDate(blog.created_at)}</div>
                        <div className="text-xs text-gray-400 flex items-center gap-1">
                          <span className="w-2 h-2 rounded-full bg-green-500"></span>
                          Published
                        </div>
                      </td>
                      <td className="px-6 py-4 text-right text-sm font-medium">
                        <div className="flex justify-end gap-2">
                          <button
                            className="p-2 rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
                            title="View"
                            onClick={() => handleView(blog)}
                          >
                            <FaEye />
                          </button>
                          <button
                            className="p-2 rounded-lg text-green-600 hover:bg-green-50 transition-colors"
                            title="Edit"
                            onClick={() => handleEdit(blog)}
                          >
                            <FaEdit />
                          </button>
                          <button
                            className="p-2 rounded-lg text-red-600 hover:bg-red-50 transition-colors"
                            title="Delete"
                            onClick={() => handleDeleteClick(blog.id)}
                          >
                            <FaTrash />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Results counter */}
        {!loading && filteredBlogs.length > 0 && (
          <div className="mt-4 text-sm text-gray-500">
            Showing {filteredBlogs.length} of {blogs.length} blogs
          </div>
        )}

        {/* Modal for view/edit */}
        {showModal && (
          <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[1499] p-4">
            <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col animate-modalIn border border-gray-200">
              <div className="flex items-center justify-between px-6 py-4 bg-white border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                  {modalMode === "view" ? (
                    <>
                      <FaEye className="text-blue-600" /> View Blog
                    </>
                  ) : (
                    <>
                      <FaEdit className="text-green-600" /> Edit Blog Content
                    </>
                  )}
                </h2>
                <button
                  className="p-2 text-gray-500 hover:text-white hover:bg-red-500 rounded-full"
                  onClick={handleModalClose}
                  title="Close"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="overflow-y-auto p-6">
                {modalMode === "view" ? (
                  blogDetails ? (
                    <div className="space-y-6">
                      <div className="flex flex-col md:flex-row gap-6">
                        {blogDetails.banner && (
                          <div className="flex-shrink-0">
                            <div className="bg-gray-100 border-2 border-dashed rounded-xl w-full md:w-64 h-48 overflow-hidden">
                              <img
                                src={
                                  blogDetails.banner.startsWith("http")
                                    ? blogDetails.banner
                                    : `https://flowkar.com${blogDetails.banner}`
                                }
                                alt="banner"
                                className="border-2 border-gray-300 p-[5px] rounded-[20px] lg:rounded-[25px] object-cover h-14 w-14 sm:h-14 sm:w-14 lg:h-16 lg:w-16"
                              />
                            </div>
                          </div>
                        )}
                        <div className="flex-1">
                          <h1 className="text-2xl font-bold text-gray-800 mb-2">
                            {blogDetails.title}
                          </h1>
                          <div className="flex flex-wrap gap-2 mb-4">
                            <span className="px-3 py-1 rounded-full bg-brand-100 text-brand-700 text-sm font-medium">
                              {blogDetails.category || "Uncategorized"}
                            </span>
                            <span className="px-3 py-1 rounded-full bg-blue-100 text-blue-700 text-sm font-medium">
                              {blogDetails.read_time || "N/A"} min read
                            </span>
                            <span className="px-3 py-1 rounded-full bg-gray-100 text-gray-700 text-sm font-medium">
                              {formatDate(blogDetails.created_at)}
                            </span>
                          </div>
                          <p className="text-gray-600">
                            Keywords: {blogDetails.keywords || "None"}
                          </p>
                        </div>
                      </div>

                      <div className="border-t pt-4 mt-4 border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-800 mb-3">
                          Content
                        </h3>
                        <div
                          className="prose max-w-none border rounded-lg p-4 bg-gray-50 text-gray-800"
                          dangerouslySetInnerHTML={{
                            __html: blogDetails.content,
                          }}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="py-10 text-center">
                      <div className="bg-red-100 border-2 border-dashed rounded-xl w-16 h-16 flex items-center justify-center mx-auto mb-4 text-red-500">
                        <FaTimes className="text-2xl" />
                      </div>
                      <p className="text-gray-700">
                        Failed to load blog details
                      </p>
                      <button
                        className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                        onClick={handleModalClose}
                      >
                        Close
                      </button>
                    </div>
                  )
                ) : blogDetails ? (
                  <form onSubmit={handleEditSubmit} className="space-y-6">
                    {/* Blog metadata fields */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Title *
                        </label>
                        <input
                          type="text"
                          name="title"
                          value={editForm.title}
                          onChange={handleEditChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 text-gray-800"
                          required
                        />
                        {modalMode === "edit" && editForm.title && (
                          <div className="mt-2 text-sm">
                            <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                              {slug}
                            </span>
                            {slugAvailable === null ? null : slugAvailable ? (
                              <span className="ml-2 text-green-600">
                                Slug available
                              </span>
                            ) : (
                              <span className="ml-2 text-red-600">
                                Slug taken
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Category
                        </label>
                        <input
                          type="text"
                          name="category"
                          value={editForm.category}
                          onChange={handleEditChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 text-gray-800"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Keywords
                        </label>
                        <input
                          type="text"
                          name="keywords"
                          value={editForm.keywords}
                          onChange={handleEditChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 text-gray-800"
                          placeholder="Separate keywords with commas"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Read Time (minutes)
                        </label>
                        <input
                          type="number"
                          name="read_time"
                          value={editForm.read_time}
                          onChange={handleEditChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 text-gray-800"
                          min="1"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Content (Click to edit)
                      </label>

                      {/* Editable HTML View - Same as view dialog */}
                      <div className="border border-gray-300 rounded-lg overflow-hidden">
                        <div className="bg-gray-50 px-4 py-2 text-sm text-gray-600 border-b border-gray-200">
                          ✏️ Click anywhere in the content below to edit
                        </div>
                        <div
                          ref={editorRef}
                          contentEditable
                          onInput={updateEditorContent}
                          onPaste={handlePaste}
                          className="min-h-96 p-6 text-lg leading-relaxed text-left bg-white text-gray-800 focus:outline-none"
                          style={{
                            lineHeight: "1.7",
                            color: "#374151",
                            fontSize: "16px",
                          }}
                          suppressContentEditableWarning={true}
                          placeholder="Edit your blog content here..."
                        />
                      </div>
                    </div>

                    <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                      <button
                        type="button"
                        className="px-5 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                        onClick={handleModalClose}
                        disabled={editLoading}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-5 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors shadow-md disabled:opacity-70"
                        disabled={editLoading}
                      >
                        {editLoading ? (
                          <span className="flex items-center gap-2">
                            <FaSyncAlt className="animate-spin" /> Saving...
                          </span>
                        ) : (
                          "Save Changes"
                        )}
                      </button>
                    </div>
                  </form>
                ) : (
                  <div className="py-10 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-brand-500 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading blog details...</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Dialog for viewing blog HTML content */}
        {viewDialogOpen && (
          <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[1499] p-4">
            <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col animate-modalIn border border-gray-200">
              <div className="flex items-center justify-between px-6 py-4 bg-white border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                  <FaEye className="text-blue-600" /> Blog Content
                </h2>
                <button
                  className="p-2 text-gray-500 hover:text-white hover:bg-red-500 rounded-full"
                  onClick={() => setViewDialogOpen(false)}
                  title="Close"
                >
                  <FaTimes />
                </button>
              </div>
              <div className="overflow-y-auto p-6" style={{ minHeight: 400 }}>
                {viewLoading ? (
                  <div className="flex flex-col items-center justify-center py-16">
                    <div className="relative">
                      {/* Main spinner */}
                      <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-brand-500 mb-4"></div>
                    </div>
                    <div className="text-center">
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">
                        Loading Blog Content
                      </h3>
                      <p className="text-gray-600 text-sm">
                        Please wait while we fetch the blog details...
                      </p>
                    </div>
                    {/* Loading dots animation */}
                    <div className="flex space-x-1 mt-4">
                      <div
                        className="w-2 h-2 bg-brand-500 rounded-full animate-bounce"
                        style={{ animationDelay: "0ms" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-brand-500 rounded-full animate-bounce"
                        style={{ animationDelay: "150ms" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-brand-500 rounded-full animate-bounce"
                        style={{ animationDelay: "300ms" }}
                      ></div>
                    </div>
                  </div>
                ) : (
                  <div
                    dangerouslySetInnerHTML={{ __html: viewDialogContent }}
                  />
                )}
              </div>
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 px-6 pb-4">
                <Button
                  onClick={() => setViewDialogOpen(false)}
                  sx={{ color: "#6b7280" }}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={handleDeleteCancel}
          BackdropProps={{
            style: {
              zIndex: 1400,
              backgroundColor: "rgba(0,0,0,0.7)",
            },
          }}
          PaperProps={{ style: { zIndex: 1500 } }}
        >
          <DialogTitle sx={{ color: "#374151" }}>Confirm Delete</DialogTitle>
          <DialogContent sx={{ color: "#6b7280" }}>
            Are you sure you want to delete this blog?
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDeleteCancel} sx={{ color: "#6b7280" }}>
              Cancel
            </Button>
            <Button
              onClick={handleDeleteConfirm}
              sx={{
                bgcolor: "#ef4444",
                color: "white",
                "&:hover": { bgcolor: "#dc2626" },
              }}
              autoFocus
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Animations */}
        <style>{`
        @import url("https://fonts.googleapis.com/css2?family=Metrophobic&display=swap");
        
        @keyframes modalIn {
          from { opacity: 0; transform: translateY(20px) scale(0.98); }
          to { opacity: 1; transform: translateY(0) scale(1); }
        }
        .animate-modalIn { animation: modalIn 0.3s ease; }
        
        /* Editable HTML view styles - Matching Create Blog section */
        [contenteditable="true"] {
          outline: none;
          font-family: 'Metrophobic', sans-serif;
          color: #3d2a26;
          line-height: 1.8;
        }
        
        [contenteditable="true"]:focus {
          outline: none;
          background-color: #fafafa;
        }
        
        [contenteditable="true"]:empty:before {
          content: attr(placeholder);
          color: #3d2a26;
          opacity: 0.5;
          pointer-events: none;
        }
        
        /* Paragraphs - matching Create Blog */
        [contenteditable="true"] p {
          font-size: 1.15rem;
          margin-bottom: 1.5em;
          line-height: 1.8;
          color: #3d2a26;
        }
        
        /* Headings - matching Create Blog */
        [contenteditable="true"] h1 {
          font-size: 2.5em;
          margin: 1.5em 0 1em;
          color: #000000;
          font-weight: bold;
        }

        [contenteditable="true"] h2 {
          font-size: 2em;
          margin: 1.5em 0 1em;
          color: #000000;
          font-weight: bold;
        }

        [contenteditable="true"] h3 {
          font-size: 1.5em;
          margin: 1.5em 0 1em;
          color: #000000;
          font-weight: bold;
        }

        /* Bold and Strong - matching Create Blog */
        [contenteditable="true"] b,
        [contenteditable="true"] strong {
          color: #000000;
          font-weight: bold;
        }
        
        /* Italic and Emphasis */
        [contenteditable="true"] i,
        [contenteditable="true"] em {
          font-style: italic;
          color: #3d2a26;
        }
        
        /* Underline */
        [contenteditable="true"] u {
          text-decoration: underline;
          color: #3d2a26;
        }
        
        /* Links - matching Create Blog */
        [contenteditable="true"] a {
          color: #1a73e8;
          text-decoration: underline;
        }
        
        /* Images - matching Create Blog */
        [contenteditable="true"] img {
          margin: 2em auto;
          border-radius: 0.75em;
          box-shadow: 0 4px 24px 0 rgba(86,61,57,0.08);
          max-width: 100%;
          height: auto;
          display: block;
        }
        
        /* Lists - matching Create Blog */
        [contenteditable="true"] ul,
        [contenteditable="true"] ol {
          font-size: 1.1rem;
          margin: 1.5em 0;
          padding-left: 1.5em;
          color: #3d2a26;
        }
        
        [contenteditable="true"] li {
          margin-bottom: 0.75em;
          color: #3d2a26;
        }
        
        /* Blockquotes - matching Create Blog */
        [contenteditable="true"] blockquote {
          font-size: 1.25rem;
          line-height: 1.6;
          border-left: 4px solid #FF7731;
          background: #f8f6f5;
          padding: 1.25em 1.5em;
          margin: 2em 0;
          border-radius: 0.5em;
          color: #000000;
        }
        
        /* Code blocks - matching Create Blog */
        [contenteditable="true"] pre {
          font-size: 1rem;
          line-height: 1.6;
          background: #f3f4f6;
          padding: 1em;
          border-radius: 0.5em;
          margin: 2em 0;
          overflow-x: auto;
          font-family: 'Courier New', monospace;
        }
        
        [contenteditable="true"] code {
          background: #f3f4f6;
          padding: 2px 4px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
        }
        
        /* Responsive design - matching Create Blog */
        @media (max-width: 768px) {
          [contenteditable="true"] p,
          [contenteditable="true"] ul,
          [contenteditable="true"] ol {
            font-size: 1rem;
          }
          [contenteditable="true"] h1 {
            font-size: 2em;
          }
          [contenteditable="true"] h2 {
            font-size: 1.5em;
          }
          [contenteditable="true"] h3 {
            font-size: 1.2em;
          }
        }
        
        @media (max-width: 480px) {
          [contenteditable="true"] p,
          [contenteditable="true"] ul,
          [contenteditable="true"] ol {
            font-size: 0.95rem;
          }
          [contenteditable="true"] h1 {
            font-size: 1.3em;
          }
          [contenteditable="true"] h2 {
            font-size: 1.1em;
          }
          [contenteditable="true"] h3 {
            font-size: 1em;
          }
        }
      `}</style>
      </div>
    </ThemeProvider>
  );
}

export default BlogManagement;
